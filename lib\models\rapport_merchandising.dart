class RapportMerchandising {
  final String? id;
  final String missionId;
  final String merchandiserId; // Added merchandiserId field
  final String titre;
  final String observations;
  final String? recommandations;
  final DateTime dateCreation;
  final DateTime dateModification;
  final String statut; // 'brouillon', 'envoye'
  final List<String> photos;

  RapportMerchandising({
    this.id,
    required this.missionId,
    required this.merchandiserId, // Added required merchandiserId
    required this.titre,
    required this.observations,
    this.recommandations,
    required this.dateCreation,
    required this.dateModification,
    this.statut = 'brouillon',
    this.photos = const [],
  });

  // Convertir un RapportMerchandising en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'missionId': missionId,
      'merchandiserId': merchandiserId,
      'titre': titre,
      'observations': observations,
      'recommandations': recommandations,
      'dateCreation': dateCreation.toIso8601String(),
      'dateModification': dateModification.toIso8601String(),
      'statut': statut,
      'photos': photos.join('|'),
    };
  }

  // Créer un RapportMerchandising à partir d'une Map de la base de données
  factory RapportMerchandising.fromMap(Map<String, dynamic> map) {
    // Clean photos to only include Google Drive URLs
    final rawPhotos = map['photos']?.split('|') ?? [];
    final cleanPhotos =
        rawPhotos
            .where(
              (photo) =>
                  photo.isNotEmpty &&
                  photo.startsWith('https://drive.google.com/'),
            )
            .toList();

    return RapportMerchandising(
      id: map['id'],
      missionId: map['missionId'],
      merchandiserId: map['merchandiserId'],
      titre: map['titre'],
      observations: map['observations'],
      recommandations: map['recommandations'],
      dateCreation: DateTime.parse(map['dateCreation']),
      dateModification: DateTime.parse(map['dateModification']),
      statut: map['statut'] ?? 'brouillon',
      photos: cleanPhotos,
    );
  }

  // Créer une copie avec des modifications
  RapportMerchandising copyWith({
    String? id,
    String? missionId,
    String? merchandiserId,
    String? titre,
    String? observations,
    String? recommandations,
    DateTime? dateCreation,
    DateTime? dateModification,
    String? statut,
    List<String>? photos,
  }) {
    return RapportMerchandising(
      id: id ?? this.id,
      missionId: missionId ?? this.missionId,
      merchandiserId: merchandiserId ?? this.merchandiserId,
      titre: titre ?? this.titre,
      observations: observations ?? this.observations,
      recommandations: recommandations ?? this.recommandations,
      dateCreation: dateCreation ?? this.dateCreation,
      dateModification: dateModification ?? this.dateModification,
      statut: statut ?? this.statut,
      photos: photos ?? this.photos,
    );
  }

  @override
  String toString() {
    return 'RapportMerchandising{id: $id, missionId: $missionId, titre: $titre, statut: $statut}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RapportMerchandising &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
